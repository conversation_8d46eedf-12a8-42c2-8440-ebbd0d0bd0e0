import { COOKIES_ACCESS_TOKEN_KEY, JWT_LIVE_TIME } from '@/constants';
import { setAuthorizationToRequest } from '@/utils/auth';
import { cookies } from 'next/headers';

export const handleLoginSuccess = async (token: string) => {
  const cookieStore = await cookies();

  cookieStore.set(COOKIES_ACCESS_TOKEN_KEY, token, {
    httpOnly: true,
    secure: process.env.NEXT_PUBLIC_ENV === 'prod',
    maxAge: JWT_LIVE_TIME,
    path: '/',
    sameSite: true,
  });

  setAuthorizationToRequest(token);
};

export const handleLogOut = async () => {
  const cookieStore = await cookies();
  cookieStore.set(COOKIES_ACCESS_TOKEN_KEY, '', {
    httpOnly: true,
    secure: process.env.NEXT_PUBLIC_ENV === 'prod',
    maxAge: 0, // Set to 0 to immediately expire the cookie
    path: '/',
    sameSite: true,
  });
};
