/**
 * Enum for all available roles in the system
 */
export enum RoleEnum {
  SUPER_ADMIN = 'super_admin',
  ADMIN = 'admin',
  // Add other roles as needed based on your backend system
}

export enum PermissionEnum {
  // Dashboard permissions
  DASHBOARD = 'dashboard',

  // User management permissions
  USERS = 'users',

  // KYC permissions
  KYC = 'kyc',

  // Wallet permissions
  WALLETS = 'wallets',
  WALLETS_COLD = 'wallets:cold',
  WALLETS_HOT = 'wallets:hot',
  WALLETS_DEPOSIT = 'wallets:deposit',
  WALLETS_TRANSACTIONS = 'wallets:transactions',
  WALLETS_TOKENS = 'wallets:tokens',

  // Transaction permissions
  TRANSACTIONS = 'transactions',
  TRANSACTIONS_PAYMENTS = 'transactions:payments',

  // Trading permissions
  TRADING_OPEN_ORDERS = 'trading:open-orders',
  TRADING_DAILY_VOLUMES = 'trading:daily-volumes',
  TRADING_USER_LEVELS = 'trading:user-levels',
  TRADING_ORDER_HISTORY = 'trading:order-history',
  TRADING_TRADES = 'trading:trades',
  TRADING_FEE_REVENUE = 'trading:fee-revenue',
  TRADING_MARKETS = 'trading:markets',
  TRADING_PAIRS = 'trading:pairs',
  TRADING_FEE_LEVELS = 'trading:fee-tiers',

  // Token permissions
  TOKENS = 'tokens',

  // System permissions
  LOGS = 'logs',

  // Notification permissions
  NOTIFICATIONS = 'notifications',
  NOTIFICATIONS_CAMPAIGNS = 'notifications:campaigns',

  // Admin management permissions
  ADMINS = 'admins',
  ROLES = 'roles',
}

export type PermissionValue = `${PermissionEnum}`;

export type RoleValue = `${RoleEnum}`;

export const ALL_PERMISSIONS = Object.values(PermissionEnum);

export const ALL_ROLES = Object.values(RoleEnum);
