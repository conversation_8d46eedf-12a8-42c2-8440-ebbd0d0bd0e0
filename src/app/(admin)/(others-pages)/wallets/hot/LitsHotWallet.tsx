'use client';

import { AppDataTable } from '@/components/tables/AppDataTable';
import { useCallback, useMemo, useRef, useState } from 'react';
import rf from '@/services/RequestFactory';
import { useWindowSize } from '@/hooks/useWindowSize';
import { THotWallet, THotWalletBalance } from '@/types/wallet';
import { formatShortAddress, formatUnixTimestamp, formatNumber } from '@/utils/format';
import { ChevronDownIcon, CopyIcon } from '@/icons';
import { copyToClipboard } from '@/utils/helper';
import { useSelector } from 'react-redux';
import { RootState } from '@/store/index';
import { TNetwork } from '@/types/network';
import { TAsset } from '@/types/asset';
import Image from 'next/image';

const BalanceItem = ({ balance }: { balance: THotWalletBalance }) => {
  const assets = useSelector((state: RootState) => state.metadata.assets);
  const networks = useSelector((state: RootState) => state.metadata.networks);

  const asset = assets.find((a: TAsset) => a.symbol === balance.asset);
  const network = networks.find((n: TNetwork) => n.symbol === balance.network);

  return (
    <div className="flex w-full items-center border-b border-gray-100 dark:border-white/[0.05]">
      <div className="text-theme-xs w-[20%] px-4 py-3 text-start text-gray-500 dark:text-gray-400">
        <div className="flex items-center gap-2">
          {asset?.logoUrl && (
            <div className="h-8 w-8 overflow-hidden rounded-full">
              <Image src={asset.logoUrl} width={32} height={32} alt={asset.name} />
            </div>
          )}
          <div>
            <div className="font-medium">{asset?.name || balance.asset || '--'}</div>
            <div className="text-sm text-gray-400">{asset?.symbol || '--'}</div>
          </div>
        </div>
      </div>
      <div className="text-theme-xs w-[15%] px-4 py-3 text-start text-gray-500 dark:text-gray-400">
        {formatNumber(balance.balance, 8)}
      </div>
      <div className="text-theme-xs w-[15%] px-4 py-3 text-start text-gray-500 dark:text-gray-400">
        {formatNumber(balance.collectTotal, 8)}
      </div>
      <div className="text-theme-xs w-[15%] px-4 py-3 text-start text-gray-500 dark:text-gray-400">
        {formatNumber(balance.withdrawalTotal, 8)}
      </div>
      <div className="text-theme-xs w-[15%] px-4 py-3 text-start text-gray-500 dark:text-gray-400">
        {formatNumber(balance.withdrawalPending, 8)}
      </div>
      <div className="text-theme-xs w-[20%] px-4 py-3 text-start text-gray-500 dark:text-gray-400">
        {network?.name || balance.network || '--'}
      </div>
    </div>
  );
};

const WalletRow = ({ wallet, networks }: { wallet: THotWallet; networks: TNetwork[] }) => {
  const [balances, setBalances] = useState<THotWalletBalance[]>([]);
  const [isExpanded, setIsExpanded] = useState(false);

  const network = networks.find((n) => n.symbol === wallet.network);

  const toggleExpandWallet = async () => {
    if (isExpanded) {
      setIsExpanded(false);
      return;
    }

    try {
      const res = await rf
        .getRequest('WalletRequest')
        .getHotWalletBalance({ walletId: wallet.walletId });
      setBalances(res || []);
      setIsExpanded(true);
    } catch (error) {
      console.error('Error fetching balance for wallet:', wallet.walletId, error);
    }
  };

  return (
    <>
      <div
        onClick={toggleExpandWallet}
        className="flex w-full cursor-pointer items-center border-b border-gray-100 dark:border-white/[0.05]"
        key={wallet.walletId}
      >
        <div className="text-theme-sm w-[20%] px-4 py-3 text-start capitalize text-gray-500 dark:text-gray-400 xl:px-10">
          {wallet.walletId || '--'}
        </div>
        <div className="text-theme-sm w-[20%] px-4 py-3 text-start text-gray-500 dark:text-gray-400">
          {formatUnixTimestamp(wallet.createdAt) || '--'}
        </div>
        <div className="text-theme-sm flex w-[20%] items-center gap-1 px-4 py-3 text-start capitalize text-gray-500 dark:text-gray-400">
          {wallet.address ? (
            <>
              {formatShortAddress(wallet.address, 4, 4)}
              <CopyIcon
                onClick={(e: any) => {
                  e.stopPropagation();
                  copyToClipboard(wallet.address);
                }}
                className="cursor-pointer"
              />
            </>
          ) : (
            '--'
          )}
        </div>
        <div className="w-[20%] px-4 py-3 text-start text-gray-500 dark:text-gray-400">
          <div className="text-theme-sm">{wallet.asset || '--'}</div>
          <div className="text-theme-xs">{network?.name || '--'}</div>
        </div>
        <div className="text-theme-sm flex w-[20%] items-center justify-between px-4 py-3 text-start capitalize text-gray-500 dark:text-gray-400">
          {wallet.type?.toLowerCase() || '--'}
          <ChevronDownIcon
            className={`transition-transform duration-200 ${isExpanded ? 'rotate-180' : ''}`}
          />
        </div>
      </div>

      {isExpanded && (
        <div className="border-b border-gray-100 bg-gray-50 px-4 pb-4 pt-2 dark:border-white/[0.05] dark:bg-white/[0.02] xl:px-10">
          <div className="flex border-b border-gray-100 dark:border-white/[0.05]">
            <div className="text-theme-xs w-[20%] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400">
              Asset
            </div>
            <div className="text-theme-xs w-[15%] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400">
              Balance
            </div>
            <div className="text-theme-xs w-[15%] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400">
              Collect Total
            </div>
            <div className="text-theme-xs w-[15%] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400">
              Withdrawal Total
            </div>
            <div className="text-theme-xs w-[15%] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400">
              Withdrawal Pending
            </div>
            <div className="text-theme-xs w-[20%] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400">
              Network
            </div>
          </div>

          <div className="custom-scrollbar max-h-[350px] overflow-y-auto rounded-md">
            {balances.length > 0 ? (
              balances.map((balance: THotWalletBalance, idx: number) => (
                <BalanceItem balance={balance} key={idx} />
              ))
            ) : (
              <div className="px-4 py-6 text-center text-gray-400 dark:text-gray-500">No Data</div>
            )}
          </div>
        </div>
      )}
    </>
  );
};

const ListHotWallet = () => {
  const dataTableRef = useRef<HTMLDivElement | null>(null);
  const { windowHeight } = useWindowSize();
  const networks = useSelector((state: RootState) => state.metadata.networks);

  const getData = async (params: any) => {
    try {
      const res = await rf.getRequest('WalletRequest').getHotWallet(params);
      return { data: res?.docs, cursor: res?.cursor };
    } catch (e: any) {
      console.error('Get Hot Wallet Error', e?.message);
      return { data: [], cursor: null };
    }
  };

  const tableHeight = useMemo(() => {
    return windowHeight - 250;
  }, [windowHeight]);

  const renderRow = useCallback(
    (wallet: THotWallet) => <WalletRow wallet={wallet} networks={networks} />,
    [networks]
  );

  return (
    <div className="overflow-hidden rounded-xl border border-gray-200 bg-white dark:border-white/[0.05] dark:bg-white/[0.03]">
      <AppDataTable
        minWidth={1102}
        ref={dataTableRef}
        getData={getData as any}
        height={tableHeight}
        renderHeader={() => (
          <div className="flex border-b border-gray-100 dark:border-white/[0.05]">
            <div className="text-theme-xs w-[20%] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400 xl:px-10">
              Wallet ID
            </div>
            <div className="text-theme-xs w-[20%] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400">
              Created At
            </div>
            <div className="text-theme-xs w-[20%] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400">
              Address
            </div>
            <div className="text-theme-xs w-[20%] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400">
              Asset/Network
            </div>
            <div className="text-theme-xs w-[20%] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400">
              Type
            </div>
          </div>
        )}
        renderRow={renderRow}
      />
    </div>
  );
};

export default ListHotWallet;
