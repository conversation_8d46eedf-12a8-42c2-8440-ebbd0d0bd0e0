'use client';
import React, { useCallback, useMemo, useRef, useState } from 'react';
import { AppDataTable } from '@/components/tables/AppDataTable';
import Badge from '@/components/ui/badge/Badge';
import { formatUnixTimestamp } from '@/utils/format';
import rf from '@/services/RequestFactory';
import { filterParams, copyToClipboard } from '@/utils/helper';
import { formatShortAddress } from '@/utils/format';
import { CopyIcon, ChevronDownIcon, RefreshIcon } from '@/icons';
import { formatNumber } from '@/utils/format';
import Label from '@/components/form/Label';
import Select from '@/components/form/Select';
import { TWithdrawWallet } from '@/types/wallet';
import { useWindowSize } from '@/hooks/useWindowSize';
import SearchInput from '@/components/ui/search/index';
import { useSelector } from 'react-redux';
import { RootState } from '@/store';

const OPTIONS_STATUS = [
  { value: 'ALL', label: 'All' },
  { value: 'COMPLETED', label: 'Completed' },
  { value: 'INVALID', label: 'Invalid' },
  { value: 'UNSIGNED', label: 'Unsigned' },
  { value: 'SIGNING', label: 'Signing' },
  { value: 'SIGNED', label: 'Signed' },
  { value: 'SENT', label: 'Sent' },
  { value: 'FAILED', label: 'Failed' },
];

const TableWalletWithdraw = () => {
  const [status, setStatus] = useState<string>('ALL');
  const dataTableRef = useRef<HTMLDivElement | null>(null);
  const [txId, setTxId] = useState<string>('');
  const [fromAddress, setFromAddress] = useState<string>('');
  const [toAddress, setToAddress] = useState<string>('');
  const [coin, setCoin] = useState<string>('');

  const { windowHeight } = useWindowSize();
  const assets = useSelector((state: RootState) => state.metadata.assets);
  const networks = useSelector((state: RootState) => state.metadata.networks);

  const optionsAsset = useMemo(
    () => [
      { label: 'All', value: '' },
      ...assets.map((asset) => ({
        label: asset.symbol,
        value: asset.symbol,
      })),
    ],
    [assets]
  );

  const getData = useCallback(
    async (params: any) => {
      try {
        const result = await rf.getRequest('WalletRequest').getWithdrawHistoryWallet(
          filterParams({
            ...params,
            status,
            txId,
            fromAddress,
            toAddress,
            coin,
          })
        );

        return {
          cursor: result?.cursor,
          data: result?.docs || [],
        };
      } catch (err) {
        console.log(err, 'get Withdraw Histories Wallet error');
        return { cursor: null, data: [] };
      }
    },
    [status, txId, fromAddress, toAddress, coin]
  );

  const onRefreshData = () => {
    (dataTableRef.current as any)?.refresh();
  };

  const tableHeight = useMemo(() => {
    return windowHeight - 450;
  }, [windowHeight]);

  return (
    <>
      <div className="mb-8 flex items-end justify-between gap-x-6">
        <div className="flex items-end gap-x-6">
          <div>
            <Label>Asset</Label>
            <div className="relative">
              <Select
                options={optionsAsset}
                defaultValue={coin}
                onChange={(value: string) => setCoin(value)}
                className="dark:bg-dark-900"
              />
              <span className="pointer-events-none absolute right-3 top-1/2 -translate-y-1/2 text-gray-500 dark:text-gray-400">
                <ChevronDownIcon />
              </span>
            </div>
          </div>
          <div>
            <Label>Status</Label>
            <div className="relative">
              <Select
                options={OPTIONS_STATUS}
                defaultValue={status}
                onChange={(value: string) => setStatus(value)}
                className="dark:bg-dark-900"
              />
              <span className="pointer-events-none absolute right-3 top-1/2 -translate-y-1/2 text-gray-500 dark:text-gray-400">
                <ChevronDownIcon />
              </span>
            </div>
          </div>
          <SearchInput setSearch={setTxId} placeholder="Search by TxID" />
          <SearchInput setSearch={setFromAddress} placeholder="Search by From Address" />
          <SearchInput setSearch={setToAddress} placeholder="Search by To Address" />
        </div>

        <div onClick={onRefreshData} className="cursor-pointer text-gray-800 dark:text-white/90">
          <RefreshIcon />
        </div>
      </div>
      <div className="overflow-hidden">
        <AppDataTable
          height={tableHeight}
          minWidth={1102}
          ref={dataTableRef}
          getData={getData as any}
          renderHeader={() => {
            return (
              <div className="flex border-b border-gray-100 dark:border-white/[0.05]">
                <div className="text-theme-xs w-[10%] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400">
                  Time
                </div>
                <div className="text-theme-xs w-[8%] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400">
                  Wallet ID
                </div>
                <div className="text-theme-xs w-[8%] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400">
                  Withdraw ID
                </div>
                <div className="text-theme-xs w-[8%] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400">
                  User ID
                </div>
                <div className="text-theme-xs w-[12%] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400">
                  Asset/Network
                </div>
                <div className="text-theme-xs w-[10%] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400">
                  Amount
                </div>
                <div className="text-theme-xs w-[11%] min-w-[140px] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400">
                  From Address
                </div>
                <div className="text-theme-xs w-[11%] min-w-[140px] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400">
                  To Address
                </div>
                <div className="text-theme-xs w-[11%] min-w-[140px] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400">
                  TxID
                </div>
                <div className="text-theme-xs w-[11%] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400">
                  Status
                </div>
              </div>
            );
          }}
          renderRow={(tx: TWithdrawWallet) => {
            const network = networks.find((n) => n.symbol === tx.network);
            return (
              <div
                key={tx.id}
                className="flex w-full items-center border-b border-gray-100 dark:border-white/[0.05]"
              >
                <div className="text-theme-sm w-[10%] items-center px-4 py-3 text-gray-500 dark:text-gray-400">
                  {formatUnixTimestamp(tx.createdAt)}
                </div>
                <div className="text-theme-sm flex w-[8%] items-center px-4 py-3 text-start capitalize text-gray-500 dark:text-gray-400">
                  {tx.walletId}
                </div>
                <div className="text-theme-sm flex w-[8%] items-center px-4 py-3 text-start capitalize text-gray-500 dark:text-gray-400">
                  {tx.withdrawalTxId}
                </div>
                <div className="text-theme-sm flex w-[8%] items-center px-4 py-3 text-start capitalize text-gray-500 dark:text-gray-400">
                  {tx.userId}
                </div>
                <div className="text-theme-sm w-[12%] items-center px-4 py-3 text-start capitalize text-gray-500 dark:text-gray-400">
                  <div className="text-theme-sm">{tx.asset}</div>
                  <div className="text-theme-xs">{network?.name}</div>
                </div>
                <div className="text-theme-sm w-[10%] items-center px-4 py-3 text-start capitalize text-gray-500 dark:text-gray-400">
                  {formatNumber(tx.amount)}
                </div>
                <div className="text-theme-sm flex w-[11%] min-w-[140px]  items-center gap-1 px-4 py-3 text-start capitalize text-gray-500 dark:text-gray-400">
                  {tx.fromAddress ? (
                    <>
                      {formatShortAddress(tx.fromAddress, 4, 4)}
                      <CopyIcon
                        onClick={() => copyToClipboard(tx.fromAddress)}
                        className="cursor-pointer"
                      />
                    </>
                  ) : (
                    '--'
                  )}
                </div>
                <div className="text-theme-sm flex w-[11%] min-w-[140px]  items-center gap-1 px-4 py-3 text-start capitalize text-gray-500 dark:text-gray-400">
                  {tx.fromAddress ? (
                    <>
                      {formatShortAddress(tx.toAddress, 4, 4)}
                      <CopyIcon
                        onClick={() => copyToClipboard(tx.toAddress)}
                        className="cursor-pointer"
                      />
                    </>
                  ) : (
                    '--'
                  )}
                </div>

                <div className="text-theme-sm flex w-[11%]  min-w-[140px] items-center gap-1 px-4 py-3 text-start capitalize text-gray-500 dark:text-gray-400">
                  {tx.txid ? (
                    <>
                      {formatShortAddress(tx.txid, 4, 4)}
                      <CopyIcon
                        onClick={() => copyToClipboard(tx.txid)}
                        className="cursor-pointer"
                      />
                    </>
                  ) : (
                    '--'
                  )}
                </div>

                <div className="text-theme-sm w-[11%] items-center px-4 py-3 text-start capitalize text-gray-500 dark:text-gray-400">
                  <Badge
                    size="sm"
                    color={
                      tx.status === 'completed'
                        ? 'success'
                        : tx.status === 'sent' || tx.status === 'signed' || tx.status === 'signing'
                        ? 'warning'
                        : 'error'
                    }
                  >
                    {tx.status}
                  </Badge>
                </div>
              </div>
            );
          }}
        />
      </div>
    </>
  );
};

export default TableWalletWithdraw;
