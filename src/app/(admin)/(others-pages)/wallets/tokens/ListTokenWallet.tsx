'use client';

import { AppDataTable } from '@/components/tables/AppDataTable';
import React, { useMemo, useRef, useState } from 'react';
import rf from '@/services/RequestFactory';
import { useWindowSize } from '@/hooks/useWindowSize';
import { formatShortAddress } from '@/utils/format';
import { TTokenWallet } from '@/types/wallet';
import { useModal } from '@/hooks/useModal';
import { EditIcon, CopyIcon } from '@/icons';
import ModalSettingsThreshold from '@/modals/ModalSettingsThreshold';
import SearchInput from '@/components/ui/search/index';
import { copyToClipboard } from '@/utils/helper';
import { filterParams } from '@/utils/helper';
import { formatNumberWithCommas } from '@/utils/format';
import { useSelector } from 'react-redux';
import { RootState } from '@/store/index';
import { TNetwork } from '@/types/network';
import Image from 'next/image';
import { TAsset } from '@/types/asset';

const TokenItem = ({
  token,
  onRefreshData,
}: {
  token: TTokenWallet;
  onRefreshData: () => void;
}) => {
  const { isOpen, openModal, closeModal } = useModal();
  const networks = useSelector((state: RootState) => state.metadata.networks);
  const assets = useSelector((state: RootState) => state.metadata.assets);

  const networkToken = useMemo(() => {
    if (token.symbol?.includes('bep20') || token.symbol?.toUpperCase() === 'BNB') {
      return 'BSC';
    }

    if (token.symbol?.includes('erc20')) {
      return 'ETH';
    }
    return token.symbol?.toUpperCase();
  }, [token.symbol]);

  const networkData = useMemo(() => {
    return networks.find((item: TNetwork) => item.symbol === networkToken);
  }, [networkToken, networks]);

  const assetData = useMemo(() => {
    return assets.find((item: TAsset) => item.symbol === token.name);
  }, [token.name, assets]);

  return (
    <>
      <div className="flex w-full items-center border-b border-gray-100 dark:border-white/[0.05]">
        <div className="text-theme-sm w-[23%] px-4 py-3 text-start capitalize text-gray-500 dark:text-gray-400 xl:px-10">
          <div className="text-theme-sm flex items-center gap-2 font-medium text-gray-500 dark:text-gray-400">
            {assetData?.logoUrl && (
              <div className="h-8 w-8 overflow-hidden rounded-full">
                <Image width={32} height={32} src={assetData?.logoUrl} alt={assetData?.name} />
              </div>
            )}
            <div>
              {assetData?.name || token.name || '--'}
              <div className="text-theme-xs flex items-center gap-1 text-gray-500 dark:text-gray-400">
                {networkData?.name}
              </div>
            </div>
          </div>
        </div>
        <div className="text-theme-sm flex w-[17%] items-center gap-2 px-4 py-3 text-gray-500 dark:text-gray-400">
          {token.contractAddress ? (
            <>
              {formatShortAddress(token.contractAddress, 4, 3)}
              <CopyIcon
                onClick={() => copyToClipboard(token.contractAddress)}
                className="cursor-pointer"
              />
            </>
          ) : (
            '--'
          )}
        </div>
        <div className="text-theme-sm w-[10%] px-4 py-3 text-start text-gray-500 dark:text-gray-400">
          {token.decimal}
        </div>
        <div className="text-theme-sm w-[15%] px-4 py-3 text-start text-gray-500 dark:text-gray-400">
          {formatNumberWithCommas(token.lowerThreshold)}
        </div>
        <div className="text-theme-sm w-[15%] px-4 py-3 text-start text-gray-500 dark:text-gray-400">
          {formatNumberWithCommas(token.middleThreshold)}
        </div>
        <div className="text-theme-sm w-[15%] px-4 py-3 text-start text-gray-500 dark:text-gray-400">
          {formatNumberWithCommas(token.upperThreshold)}
        </div>
        <div className="text-theme-sm w-[15%] px-4 py-3 text-start text-gray-500 dark:text-gray-400">
          {formatNumberWithCommas(token.minimumCollectAmount)}
        </div>
        <div className="text-theme-sm flex w-[10%] justify-center px-4 py-3 text-gray-500 dark:text-gray-400">
          <EditIcon onClick={openModal} className="cursor-pointer" />
        </div>
      </div>

      <ModalSettingsThreshold
        isOpen={isOpen}
        onClose={closeModal}
        token={token}
        onRefreshData={onRefreshData}
      />
    </>
  );
};

const ListTokenWallet = () => {
  const [search, setSearch] = useState<string>('');

  const dataTableRef = useRef<HTMLDivElement | null>(null);
  const { windowHeight } = useWindowSize();

  const getData = async (params: any) => {
    try {
      const res = await rf.getRequest('WalletRequest').getTokensWallet(
        filterParams({
          ...params,
          search,
        })
      );
      return { data: Array.isArray(res) ? res : [], cursor: null };
    } catch (e: any) {
      console.error('Get Tokens Wallet Error', e?.message);
      return [];
    }
  };

  const tableHeight = useMemo(() => {
    return windowHeight - 350;
  }, [windowHeight]);

  const onRefreshData = () => {
    (dataTableRef.current as any)?.refresh();
  };

  return (
    <>
      <div className="mb-8 flex items-end justify-between gap-x-6">
        <SearchInput setSearch={setSearch} placeholder="Search by name" />
      </div>
      <div className="overflow-hidden rounded-xl border border-gray-200 bg-white dark:border-white/[0.05] dark:bg-white/[0.03]">
        <AppDataTable
          minWidth={1102}
          ref={dataTableRef}
          getData={getData as any}
          height={tableHeight}
          renderHeader={() => (
            <div className="flex border-b border-gray-100 dark:border-white/[0.05]">
              <div className="text-theme-xs w-[23%] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400 xl:px-10">
                Name/Network
              </div>
              <div className="text-theme-xs w-[17%] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400">
                Contract Address
              </div>
              <div className="text-theme-xs w-[10%] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400">
                Decimals
              </div>
              <div className="text-theme-xs w-[15%] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400">
                Lower Threshold
              </div>
              <div className="text-theme-xs w-[15%] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400">
                Middle Threshold
              </div>
              <div className="text-theme-xs w-[15%] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400">
                Upper Threshold
              </div>
              <div className="text-theme-xs w-[15%] px-4 py-3 text-start font-medium text-gray-500 dark:text-gray-400">
                Minimum Collect Amount
              </div>
              <div className="text-theme-xs w-[10%] px-4 py-3 text-center font-medium text-gray-500 dark:text-gray-400">
                Action
              </div>
            </div>
          )}
          renderRow={(token: TTokenWallet, index: number) => (
            <TokenItem token={token} key={index} onRefreshData={onRefreshData} />
          )}
        />
      </div>
    </>
  );
};

export default ListTokenWallet;
