'use client';

import { useState } from 'react';
import TabBar from '@/components/ui/tab';
import TableKYCVerificationPending from './TableKYCVerificationPending';
import TableKYCVerificationApproved from './TableKYCVerificationApproved';
import TableKYCVerificationRejected from './TableKYCVerificationRejected';

const tabs = [
  {
    label: 'Approved',
    value: 'approved',
  },
  {
    label: 'Rejected',
    value: 'rejected',
  },
  {
    label: 'Pending',
    value: 'pending',
  },
];

const KYCVerificationContent = () => {
  const [activeTab, setActiveTab] = useState<string>('approved');
  return (
    <div className="rounded-2xl bg-white dark:bg-white/[0.03] ">
      <div className="space-y-6">
        <div>
          <TabBar tabs={tabs} activeTab={activeTab} setActiveTab={setActiveTab} />
          <div className="rounded-b-xl border border-t-0 border-gray-200 p-6 pt-4 dark:border-gray-800">
            {activeTab === 'pending' && <TableKYCVerificationPending />}
            {activeTab === 'approved' && <TableKYCVerificationApproved />}
            {activeTab === 'rejected' && <TableKYCVerificationRejected />}
          </div>
        </div>
      </div>
    </div>
  );
};

export default KYCVerificationContent;
