'use client';
import React, { useEffect, useRef, useState, useCallback, useMemo } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { usePathname } from 'next/navigation';
import { useSelector } from 'react-redux';
import { useSidebar } from '../context/SidebarContext';
import { RootState } from '@/store/index';
import { canAccessNavItem } from '@/utils/permissions';
import { PermissionEnum } from '@/types/enums';
import SidebarSkeleton from '@/components/ui/skeleton/SidebarSkeleton';
import {
  BoxCubeIcon,
  ChevronDownIcon,
  GridIcon,
  HorizontaLDots,
  ListIcon,
  KYCIcon,
  PieChartIcon,
  PlugInIcon,
  TableIcon,
  UserCircleIcon,
  DocsIcon,
  GroupIcon,
} from '../icons/index';

type NavItem = {
  name: string;
  icon: React.ReactNode;
  path?: string;
  subItems?: {
    name: string;
    path: string;
    pro?: boolean;
    new?: boolean;
    requiredPermissions?: PermissionEnum[];
  }[];
  requiredPermissions?: PermissionEnum[];
};

const navItems: NavItem[] = [
  {
    icon: <GridIcon />,
    name: 'Dashboard',
    path: '/',
    requiredPermissions: [PermissionEnum.DASHBOARD],
  },
  {
    icon: <UserCircleIcon />,
    name: 'User Management',
    path: '/users',
    requiredPermissions: [PermissionEnum.USERS],
  },
  {
    icon: <KYCIcon />,
    name: 'KYC Verification',
    path: '/kyc',
    requiredPermissions: [PermissionEnum.KYC],
  },

  {
    name: 'Wallet Management',
    icon: <ListIcon />,
    requiredPermissions: [PermissionEnum.WALLETS],
    subItems: [
      {
        name: 'Cold Wallets',
        path: '/wallets/cold',
        pro: false,
        requiredPermissions: [PermissionEnum.WALLETS_COLD],
      },
      {
        name: 'Hot Wallets',
        path: '/wallets/hot',
        pro: false,
        requiredPermissions: [PermissionEnum.WALLETS_HOT],
      },
      {
        name: 'Deposit Wallets',
        path: '/wallets/deposit-wallets',
        pro: false,
        requiredPermissions: [PermissionEnum.WALLETS_DEPOSIT],
      },
      {
        name: 'Transaction History',
        path: '/wallets/transactions',
        pro: false,
        requiredPermissions: [PermissionEnum.WALLETS_TRANSACTIONS],
      },
      {
        name: 'Tokens',
        path: '/wallets/tokens',
        pro: false,
        requiredPermissions: [PermissionEnum.WALLETS_TOKENS],
      },
    ],
  },
  {
    name: 'Transactions Monitoring',
    icon: <TableIcon />,
    requiredPermissions: [PermissionEnum.TRANSACTIONS, PermissionEnum.TRANSACTIONS_PAYMENTS],
    subItems: [
      {
        name: 'Transactions History',
        path: '/exchange/transactions',
        pro: false,
        requiredPermissions: [PermissionEnum.TRANSACTIONS],
      },
      {
        name: 'Payment History',
        path: '/exchange/payments',
        pro: false,
        requiredPermissions: [PermissionEnum.TRANSACTIONS_PAYMENTS],
      },
    ],
  },
  {
    name: 'Trading Monitoring',
    icon: <TableIcon />,
    path: '/trading',
    requiredPermissions: [
      PermissionEnum.TRADING_OPEN_ORDERS,
      PermissionEnum.TRADING_ORDER_HISTORY,
      PermissionEnum.TRADING_TRADES,
      PermissionEnum.TRADING_FEE_REVENUE,
      PermissionEnum.TRADING_DAILY_VOLUMES,
      PermissionEnum.TRADING_USER_LEVELS,
    ],
    subItems: [
      {
        name: 'Open Orders',
        path: '/trading/open-orders',
        pro: false,
        requiredPermissions: [PermissionEnum.TRADING_OPEN_ORDERS],
      },
      {
        name: 'Order History',
        path: '/trading/order-histories',
        pro: false,
        requiredPermissions: [PermissionEnum.TRADING_ORDER_HISTORY],
      },
      {
        name: 'Trades',
        path: '/trading/trades',
        pro: false,
        requiredPermissions: [PermissionEnum.TRADING_TRADES],
      },
      // {
      //   name: 'Trading Fee Revenue',
      //   path: '/trading/fee-revenue',
      //   pro: false,
      //   requiredPermissions: [PermissionEnum.TRADING_FEE_REVENUE],
      // },
      {
        name: 'Daily Volumes',
        path: '/trading/daily-volumes',
        pro: false,
        requiredPermissions: [PermissionEnum.TRADING_DAILY_VOLUMES],
      },
      {
        name: 'User Levels',
        path: '/trading/user-levels',
        pro: false,
        requiredPermissions: [PermissionEnum.TRADING_USER_LEVELS],
      },
    ],
  },
  {
    name: 'Trading Settings',
    icon: (
      <svg
        className="fill-gray-500 group-hover:fill-gray-700 dark:fill-gray-400 dark:group-hover:fill-gray-300"
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M10.4858 3.5L13.5182 3.5C13.9233 3.5 14.2518 3.82851 14.2518 4.23377C14.2518 5.9529 16.1129 7.02795 17.602 6.1682C17.9528 5.96567 18.4014 6.08586 18.6039 6.43667L20.1203 9.0631C20.3229 9.41407 20.2027 9.86286 19.8517 10.0655C18.3625 10.9253 18.3625 13.0747 19.8517 13.9345C20.2026 14.1372 20.3229 14.5859 20.1203 14.9369L18.6039 17.5634C18.4013 17.9142 17.9528 18.0344 17.602 17.8318C16.1129 16.9721 14.2518 18.0471 14.2518 19.7663C14.2518 20.1715 13.9233 20.5 13.5182 20.5H10.4858C10.0804 20.5 9.75182 20.1714 9.75182 19.766C9.75182 18.0461 7.88983 16.9717 6.40067 17.8314C6.04945 18.0342 5.60037 17.9139 5.39767 17.5628L3.88167 14.937C3.67903 14.586 3.79928 14.1372 4.15026 13.9346C5.63949 13.0748 5.63946 10.9253 4.15025 10.0655C3.79926 9.86282 3.67901 9.41401 3.88165 9.06303L5.39764 6.43725C5.60034 6.08617 6.04943 5.96581 6.40065 6.16858C7.88982 7.02836 9.75182 5.9539 9.75182 4.23399C9.75182 3.82862 10.0804 3.5 10.4858 3.5ZM13.5182 2L10.4858 2C9.25201 2 8.25182 3.00019 8.25182 4.23399C8.25182 4.79884 7.64013 5.15215 7.15065 4.86955C6.08213 4.25263 4.71559 4.61859 4.0986 5.68725L2.58261 8.31303C1.96575 9.38146 2.33183 10.7477 3.40025 11.3645C3.88948 11.647 3.88947 12.3531 3.40026 12.6355C2.33184 13.2524 1.96578 14.6186 2.58263 15.687L4.09863 18.3128C4.71562 19.3814 6.08215 19.7474 7.15067 19.1305C7.64015 18.8479 8.25182 19.2012 8.25182 19.766C8.25182 20.9998 9.25201 22 10.4858 22H13.5182C14.7519 22 15.7518 20.9998 15.7518 19.7663C15.7518 19.2015 16.3632 18.8487 16.852 19.1309C17.9202 19.7476 19.2862 19.3816 19.9029 18.3134L21.4193 15.6869C22.0361 14.6185 21.6701 13.2523 20.6017 12.6355C20.1125 12.3531 20.1125 11.647 20.6017 11.3645C21.6701 10.7477 22.0362 9.38152 21.4193 8.3131L19.903 5.68667C19.2862 4.61842 17.9202 4.25241 16.852 4.86917C16.3632 5.15138 15.7518 4.79856 15.7518 4.23377C15.7518 3.00024 14.7519 2 13.5182 2ZM9.6659 11.9999C9.6659 10.7103 10.7113 9.66493 12.0009 9.66493C13.2905 9.66493 14.3359 10.7103 14.3359 11.9999C14.3359 13.2895 13.2905 14.3349 12.0009 14.3349C10.7113 14.3349 9.6659 13.2895 9.6659 11.9999ZM12.0009 8.16493C9.88289 8.16493 8.1659 9.88191 8.1659 11.9999C8.1659 14.1179 9.88289 15.8349 12.0009 15.8349C14.1189 15.8349 15.8359 14.1179 15.8359 11.9999C15.8359 9.88191 14.1189 8.16493 12.0009 8.16493Z"
          fill=""
        />
      </svg>
    ),
    path: '/trading',
    requiredPermissions: [
      PermissionEnum.TRADING_MARKETS,
      PermissionEnum.TRADING_PAIRS,
      PermissionEnum.TRADING_FEE_LEVELS,
    ],
    subItems: [
      {
        name: 'Markets',
        path: '/trading/markets',
        pro: false,
        requiredPermissions: [PermissionEnum.TRADING_MARKETS],
      },
      {
        name: 'Trading Pairs',
        path: '/trading/pairs',
        pro: false,
        requiredPermissions: [PermissionEnum.TRADING_PAIRS],
      },
      {
        name: 'Trading Fee Levels',
        path: '/trading/fee-levels',
        pro: false,
        requiredPermissions: [PermissionEnum.TRADING_FEE_LEVELS],
      },
    ],
  },
  {
    name: 'Token Management',
    icon: <BoxCubeIcon />,
    path: '/tokens',
    requiredPermissions: [PermissionEnum.TOKENS],
  },
  // {
  //   name: 'System Logs',
  //   icon: <DocsIcon />,
  //   path: '/logs',
  //   requiredPermissions: [PermissionEnum.LOGS],
  // },
  // {
  //   name: 'Notification',
  //   icon: (
  //     <svg
  //       className="fill-current"
  //       width="20"
  //       height="20"
  //       viewBox="0 0 20 20"
  //       xmlns="http://www.w3.org/2000/svg"
  //     >
  //       <path
  //         fillRule="evenodd"
  //         clipRule="evenodd"
  //         d="M10.75 2.29248C10.75 1.87827 10.4143 1.54248 10 1.54248C9.58583 1.54248 9.25004 1.87827 9.25004 2.29248V2.83613C6.08266 3.20733 3.62504 5.9004 3.62504 9.16748V14.4591H3.33337C2.91916 14.4591 2.58337 14.7949 2.58337 15.2091C2.58337 15.6234 2.91916 15.9591 3.33337 15.9591H4.37504H15.625H16.6667C17.0809 15.9591 17.4167 15.6234 17.4167 15.2091C17.4167 14.7949 17.0809 14.4591 16.6667 14.4591H16.375V9.16748C16.375 5.9004 13.9174 3.20733 10.75 2.83613V2.29248ZM14.875 14.4591V9.16748C14.875 6.47509 12.6924 4.29248 10 4.29248C7.30765 4.29248 5.12504 6.47509 5.12504 9.16748V14.4591H14.875ZM8.00004 17.7085C8.00004 18.1228 8.33583 18.4585 8.75004 18.4585H11.25C11.6643 18.4585 12 18.1228 12 17.7085C12 17.2943 11.6643 16.9585 11.25 16.9585H8.75004C8.33583 16.9585 8.00004 17.2943 8.00004 17.7085Z"
  //         fill="currentColor"
  //       />
  //     </svg>
  //   ),
  //   requiredPermissions: [PermissionEnum.NOTIFICATIONS],
  //   subItems: [
  //     {
  //       name: 'Campaigns',
  //       path: '/notification/campaigns',
  //       pro: false,
  //       requiredPermissions: [PermissionEnum.NOTIFICATIONS_CAMPAIGNS],
  //     },
  //   ],
  // },
  {
    name: 'Admin Management',
    icon: <GroupIcon />,
    requiredPermissions: [PermissionEnum.ADMINS, PermissionEnum.ROLES],
    subItems: [
      {
        name: 'Admins',
        path: '/admins',
        pro: false,
        requiredPermissions: [PermissionEnum.ADMINS],
      },
      {
        name: 'Roles',
        path: '/roles',
        pro: false,
        requiredPermissions: [PermissionEnum.ROLES],
      },
    ],
  },
];

const othersItems: NavItem[] = [
  {
    icon: <PieChartIcon />,
    name: 'Charts',
    subItems: [
      { name: 'Line Chart', path: '/line-chart', pro: false },
      { name: 'Bar Chart', path: '/bar-chart', pro: false },
    ],
  },
  {
    icon: <BoxCubeIcon />,
    name: 'UI Elements',
    subItems: [
      { name: 'Alerts', path: '/alerts', pro: false },
      { name: 'Avatar', path: '/avatars', pro: false },
      { name: 'Badge', path: '/badge', pro: false },
      { name: 'Buttons', path: '/buttons', pro: false },
      { name: 'Images', path: '/images', pro: false },
      { name: 'Videos', path: '/videos', pro: false },
      { name: 'Form Elements', path: '/form-elements', pro: false },
    ],
  },
  {
    icon: <PlugInIcon />,
    name: 'Authentication',
    subItems: [
      { name: 'Sign In', path: '/signin', pro: false },
      { name: 'Sign Up', path: '/signup', pro: false },
    ],
  },
];

const AppSidebar: React.FC = () => {
  const { isExpanded, isMobileOpen, isHovered, setIsHovered } = useSidebar();
  const pathname = usePathname();

  const userInfo = useSelector((state: RootState) => state.user.userInfo);
  const isUserLoading = useSelector((state: RootState) => state.user.loading);

  // Filter navigation items based on user permissions
  const filterNavItems = (items: NavItem[]): NavItem[] => {
    return items
      .map((item) => {
        // Filter sub-items first if they exist
        if (item.subItems) {
          const filteredSubItems = item.subItems.filter((subItem) =>
            canAccessNavItem(userInfo, subItem.requiredPermissions)
          );

          if (
            !canAccessNavItem(userInfo, item.requiredPermissions) ||
            (filteredSubItems.length === 0 && !item.path)
          ) {
            return null;
          }

          return { ...item, subItems: filteredSubItems };
        }

        // For items without sub-items, just check the item's permissions
        return canAccessNavItem(userInfo, item.requiredPermissions) ? item : null;
      })
      .filter((item): item is NavItem => item !== null);
  };

  // Get filtered navigation items
  const filteredNavItems = useMemo(() => filterNavItems(navItems), [userInfo]);
  const filteredOthersItems = useMemo(() => filterNavItems(othersItems), [userInfo]);

  const renderMenuItems = (navItems: NavItem[], menuType: 'main' | 'others') => (
    <ul className="flex flex-col gap-4">
      {navItems.map((nav, index) => (
        <li key={nav.name}>
          {nav.subItems ? (
            <button
              onClick={() => handleSubmenuToggle(index, menuType)}
              className={`menu-item group  ${
                openSubmenu?.type === menuType && openSubmenu?.index === index
                  ? 'menu-item-active'
                  : 'menu-item-inactive'
              } cursor-pointer ${
                !isExpanded && !isHovered ? 'lg:justify-center' : 'lg:justify-start'
              }`}
            >
              <span
                className={` ${
                  openSubmenu?.type === menuType && openSubmenu?.index === index
                    ? 'menu-item-icon-active'
                    : 'menu-item-icon-inactive'
                }`}
              >
                {nav.icon}
              </span>
              {(isExpanded || isHovered || isMobileOpen) && (
                <span className={`menu-item-text`}>{nav.name}</span>
              )}
              {(isExpanded || isHovered || isMobileOpen) && (
                <ChevronDownIcon
                  className={`ml-auto h-5 w-5 transition-transform duration-200  ${
                    openSubmenu?.type === menuType && openSubmenu?.index === index
                      ? 'text-brand-500 rotate-180'
                      : ''
                  }`}
                />
              )}
            </button>
          ) : (
            nav.path && (
              <Link
                href={nav.path}
                className={`menu-item group ${
                  isActive(nav.path) ? 'menu-item-active' : 'menu-item-inactive'
                }  ${!isExpanded && !isHovered ? 'lg:justify-center' : 'lg:justify-start'}`}
              >
                <span
                  className={`${
                    isActive(nav.path) ? 'menu-item-icon-active' : 'menu-item-icon-inactive'
                  }`}
                >
                  {nav.icon}
                </span>
                {(isExpanded || isHovered || isMobileOpen) && (
                  <span className={`menu-item-text`}>{nav.name}</span>
                )}
              </Link>
            )
          )}
          {nav.subItems && (isExpanded || isHovered || isMobileOpen) && (
            <div
              ref={(el) => {
                subMenuRefs.current[`${menuType}-${index}`] = el;
              }}
              className="overflow-hidden transition-all duration-300"
              style={{
                height:
                  openSubmenu?.type === menuType && openSubmenu?.index === index
                    ? `${subMenuHeight[`${menuType}-${index}`]}px`
                    : '0px',
              }}
            >
              <ul className="ml-9 mt-2 space-y-1">
                {nav.subItems.map((subItem) => (
                  <li key={subItem.name}>
                    <Link
                      href={subItem.path}
                      className={`menu-dropdown-item ${
                        isActive(subItem.path)
                          ? 'menu-dropdown-item-active'
                          : 'menu-dropdown-item-inactive'
                      }`}
                    >
                      {subItem.name}
                      <span className="ml-auto flex items-center gap-1">
                        {subItem.new && (
                          <span
                            className={`ml-auto ${
                              isActive(subItem.path)
                                ? 'menu-dropdown-badge-active'
                                : 'menu-dropdown-badge-inactive'
                            } menu-dropdown-badge `}
                          >
                            new
                          </span>
                        )}
                        {subItem.pro && (
                          <span
                            className={`ml-auto ${
                              isActive(subItem.path)
                                ? 'menu-dropdown-badge-active'
                                : 'menu-dropdown-badge-inactive'
                            } menu-dropdown-badge `}
                          >
                            pro
                          </span>
                        )}
                      </span>
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          )}
        </li>
      ))}
    </ul>
  );

  const [openSubmenu, setOpenSubmenu] = useState<{
    type: 'main' | 'others';
    index: number;
  } | null>(null);
  const [subMenuHeight, setSubMenuHeight] = useState<Record<string, number>>({});
  const subMenuRefs = useRef<Record<string, HTMLDivElement | null>>({});

  // const isActive = (path: string) => path === pathname;
  const isActive = useCallback((path: string) => path === pathname, [pathname]);

  useEffect(() => {
    // Check if the current path matches any submenu item
    let submenuMatched = false;
    ['main', 'others'].forEach((menuType) => {
      const items = menuType === 'main' ? filteredNavItems : filteredOthersItems;
      items.forEach((nav, index) => {
        if (nav.subItems) {
          nav.subItems.forEach((subItem) => {
            if (isActive(subItem.path)) {
              setOpenSubmenu({
                type: menuType as 'main' | 'others',
                index,
              });
              submenuMatched = true;
            }
          });
        }
      });
    });

    // If no submenu item matches, close the open submenu
    if (!submenuMatched) {
      setOpenSubmenu(null);
    }
  }, [pathname, isActive, filteredNavItems, filteredOthersItems]);

  useEffect(() => {
    // Set the height of the submenu items when the submenu is opened
    if (openSubmenu !== null) {
      const key = `${openSubmenu.type}-${openSubmenu.index}`;
      if (subMenuRefs.current[key]) {
        setSubMenuHeight((prevHeights) => ({
          ...prevHeights,
          [key]: subMenuRefs.current[key]?.scrollHeight || 0,
        }));
      }
    }
  }, [openSubmenu]);

  const handleSubmenuToggle = (index: number, menuType: 'main' | 'others') => {
    setOpenSubmenu((prevOpenSubmenu) => {
      if (prevOpenSubmenu && prevOpenSubmenu.type === menuType && prevOpenSubmenu.index === index) {
        return null;
      }
      return { type: menuType, index };
    });
  };

  return (
    <aside
      className={`fixed left-0 top-0 z-[100] mt-16 flex h-screen flex-col border-r border-gray-200 bg-white px-3 text-gray-900 transition-all duration-300 ease-in-out dark:border-gray-800 dark:bg-gray-900 lg:mt-0
        ${isExpanded || isMobileOpen ? 'w-[290px]' : isHovered ? 'w-[290px]' : 'w-[90px]'}
        ${isMobileOpen ? 'translate-x-0' : '-translate-x-full'}
        lg:translate-x-0`}
      onMouseEnter={() => !isExpanded && setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <div
        className={`flex py-6  ${
          !isExpanded && !isHovered ? 'lg:justify-center' : 'justify-start'
        }`}
      >
        <Link href="/">
          {isExpanded || isHovered || isMobileOpen ? (
            <>
              <Image
                className="dark:hidden"
                src="/images/logo/logo.svg"
                alt="Logo"
                width={120}
                height={40}
              />
              <Image
                className="hidden dark:block"
                src="/images/logo/logo-dark.svg"
                alt="Logo"
                width={120}
                height={40}
              />
            </>
          ) : (
            <Image src="/images/logo/logo-icon.svg" alt="Logo" width={32} height={32} />
          )}
        </Link>
      </div>
      <div className="no-scrollbar flex flex-col overflow-y-auto duration-300 ease-linear">
        {isUserLoading ? (
          // Skeleton loading state
          <nav className="mb-6">
            <SidebarSkeleton
              isExpanded={isExpanded}
              isHovered={isHovered}
              isMobileOpen={isMobileOpen}
            />
          </nav>
        ) : (
          <nav className="mb-6">
            <div className="flex flex-col gap-4">
              <div>
                <h2
                  className={`mb-4 flex text-xs uppercase leading-[20px] text-gray-400 ${
                    !isExpanded && !isHovered ? 'lg:justify-center' : 'justify-start'
                  }`}
                >
                  {isExpanded || isHovered || isMobileOpen ? 'Menu' : <HorizontaLDots />}
                </h2>
                {renderMenuItems(filteredNavItems, 'main')}
              </div>

              {/*{filteredOthersItems.length > 0 && (*/}
              {/*  <div className="">*/}
              {/*    <h2*/}
              {/*      className={`mb-4 flex text-xs uppercase leading-[20px] text-gray-400 ${*/}
              {/*        !isExpanded && !isHovered ? 'lg:justify-center' : 'justify-start'*/}
              {/*      }`}*/}
              {/*    >*/}
              {/*      {isExpanded || isHovered || isMobileOpen ? 'Others' : <HorizontaLDots />}*/}
              {/*    </h2>*/}
              {/*    {renderMenuItems(filteredOthersItems, 'others')}*/}
              {/*  </div>*/}
              {/*)}*/}
            </div>
          </nav>
        )}
      </div>
    </aside>
  );
};

export default AppSidebar;
