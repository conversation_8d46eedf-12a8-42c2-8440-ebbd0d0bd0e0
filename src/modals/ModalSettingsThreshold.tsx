import { AppDrawer } from '@/components/ui/modal/AppDrawer';
import React, { useEffect } from 'react';
import { TTokenWallet } from '@/types/wallet';
import { formatShortAddress } from '@/utils/format';
import { useForm } from 'react-hook-form';
import rf from '@/services/RequestFactory';
import { toastSuccess } from '@/libs/toast';
import Button from '@/components/ui/button/Button';
import Input from '@/components/form/input/InputField';
import Label from '@/components/form/Label';
import BigNumber from 'bignumber.js';
import { toastError } from '../libs/toast';

const ModalSettingsThreshold = ({
  isOpen,
  onClose,
  token,
  onRefreshData,
}: {
  isOpen: boolean;
  onClose: () => void;
  onRefreshData: () => void;
  token: TTokenWallet;
}) => {
  const {
    register,
    handleSubmit,
    formState: { isSubmitting },
    reset,
  } = useForm({
    defaultValues: {
      lowerThreshold: '',
      upperThreshold: '',
      minimumCollectAmount: '',
    },
  });

  const onSubmit = async (data: any) => {
    try {
      await rf.getRequest('WalletRequest').settingsThreshold({
        currency: token.symbol,
        lowerThreshold: data.lowerThreshold,
        upperThreshold: data.upperThreshold,
        minimumCollectAmount: data.minimumCollectAmount,
      });
      toastSuccess('Success', 'Settings Threshold Successfully!');
      onRefreshData();
    } catch (e: any) {
      toastError('Error', e?.message || 'Something went wrong!');
      console.error('Settings Threshold Error', e?.message);
    }
  };

  useEffect(() => {
    if (isOpen) {
      reset({
        lowerThreshold: new BigNumber(token.lowerThreshold).toFixed(),
        upperThreshold: new BigNumber(token.upperThreshold).toFixed(),
        minimumCollectAmount: new BigNumber(token.minimumCollectAmount || '0').toFixed(),
      });
    }
  }, [isOpen, reset, token]);

  return (
    <AppDrawer isOpen={isOpen} toggleDrawer={onClose} className="lg:!w-[350px] ">
      <div className="flex items-center justify-between border-b border-gray-100 p-4 dark:border-white/[0.05]">
        Settings Threshold
        <button onClick={onClose}>
          <svg
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              fillRule="evenodd"
              clipRule="evenodd"
              d="M6.04289 16.5413C5.65237 16.9318 5.65237 17.565 6.04289 17.9555C6.43342 18.346 7.06658 18.346 7.45711 17.9555L11.9987 13.4139L16.5408 17.956C16.9313 18.3466 17.5645 18.3466 17.955 17.956C18.3455 17.5655 18.3455 16.9323 17.955 16.5418L13.4129 11.9997L17.955 7.4576C18.3455 7.06707 18.3455 6.43391 17.955 6.04338C17.5645 5.65286 16.9313 5.65286 16.5408 6.04338L11.9987 10.5855L7.45711 6.0439C7.06658 5.65338 6.43342 5.65338 6.04289 6.0439C5.65237 6.43442 5.65237 7.06759 6.04289 7.45811L10.5845 11.9997L6.04289 16.5413Z"
              fill="currentColor"
            />
          </svg>
        </button>
      </div>

      <div className="flex items-center gap-3 p-4">
        <div>
          <div>{token.name || '--'}</div>
          <div className="text-theme-xs text-gray-500 dark:text-gray-400">
            {formatShortAddress(token.contractAddress)}
          </div>
        </div>
      </div>

      <form onSubmit={handleSubmit(onSubmit)}>
        <div className="mb-6">
          <div className="col-span-2 mx-4">
            <Label>Lower Threshold</Label>
            <Input
              decimals={8}
              {...register('lowerThreshold')}
              type="number"
              step="any"
              placeholder="Enter Lower Threshold"
            />
          </div>
          <div className="col-span-2 mx-4 mt-4">
            <Label>Upper Threshold</Label>
            <Input
              decimals={8}
              {...register('upperThreshold')}
              type="number"
              step="any"
              placeholder="Enter Upper Threshold"
            />
          </div>
          <div className="col-span-2 mx-4 mt-4">
            <Label>Minimum Collect Amount</Label>
            <Input
              decimals={8}
              {...register('minimumCollectAmount')}
              type="number"
              step="any"
              placeholder="Enter Minimum Collect Amount"
            />
          </div>
        </div>
        <div className="mx-4 flex justify-end gap-3">
          <Button size="sm" variant="outline" onClick={onClose} disabled={isSubmitting}>
            Cancel
          </Button>
          <Button size="sm" type="submit" disabled={isSubmitting}>
            Save Changes
          </Button>
        </div>
      </form>
    </AppDrawer>
  );
};

export default ModalSettingsThreshold;
