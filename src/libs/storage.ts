const PREFERENCES = `vdax-admin`;

type StorageInterface = {
  accessToken?: string;
  refreshToken?: string;
};

const defaultPreferences: StorageInterface = {};

function getStorage(): StorageInterface | any {
  if (typeof window === 'undefined') return {};
  const preferencesString = localStorage.getItem(PREFERENCES);
  const preferences = JSON.parse(preferencesString || '{}');
  return {
    ...defaultPreferences,
    ...preferences,
  };
}

function setStorage(type: string, value: StorageInterface) {
  if (typeof window === 'undefined') return;
  localStorage.setItem(type, JSON.stringify(value));
}

class Storage {
  static init() {
    const preferences = getStorage();
    setStorage(PREFERENCES, preferences);
  }

  static getAccessToken(): string | undefined {
    const { accessToken } = getStorage();
    return accessToken;
  }

  static setAccessToken(accessToken: string) {
    const preferences = getStorage();
    preferences.accessToken = accessToken;
    setStorage(PREFERENCES, preferences);
  }

  static getRefreshToken(): string | undefined {
    const { refreshToken } = getStorage();
    return refreshToken;
  }

  static setRefreshToken(refreshToken: string) {
    const preferences = getStorage();
    preferences.refreshToken = refreshToken;
    setStorage(PREFERENCES, preferences);
  }

  static clearTokens() {
    const preferences = getStorage();
    delete preferences.accessToken;
    delete preferences.refreshToken;
    setStorage(PREFERENCES, preferences);
  }
}

export default Storage;
