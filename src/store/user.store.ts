import { setAuthorizationToRequest } from '@/utils/auth';
import { createSlice } from '@reduxjs/toolkit';
import rf from '@/services/RequestFactory';
import { createAsyncThunk } from '@reduxjs/toolkit';
import { RoleEnum } from '@/types/enums';
import { TPermission } from '@/types/permission';

export type TUser = {
  admin_id: number;
  name: string;
  email: string;
  status: string;
  roles: RoleEnum[];
  permissions: TPermission[];
};

export type UserState = {
  userInfo: TUser;
  loading: boolean;
  error: string | null;
};

const initialState: UserState = {
  userInfo: {} as TUser,
  loading: false,
  error: null,
};

const userSlice = createSlice({
  name: 'user',
  initialState,
  reducers: {
    setUserInfo: (state, action) => {
      const { user } = action.payload;
      state.userInfo = user;
    },
    clearUser: () => {
      setAuthorizationToRequest(null);
      return {
        userInfo: {} as TUser,
        loading: false,
        error: null,
      };
    },
  },
  extraReducers: (builder) => {
    builder
      // getUserProfile
      .addCase(getUserProfile.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getUserProfile.fulfilled, (state, action) => {
        state.loading = false;
        state.userInfo = action.payload;
      })
      .addCase(getUserProfile.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

export const getUserProfile = createAsyncThunk(
  'user/getUserProfile',
  async (_, { rejectWithValue, dispatch }) => {
    try {
      const res = await rf.getRequest('AdminRequest').getAccountProfile();
      return res;
    } catch (error: unknown) {
      return rejectWithValue('Failed to fetch user profile');
    }
  }
);

export const logoutUser = createAsyncThunk('user/logoutUser', async (_, { dispatch }) => {
  try {
    // Call the logout API to clear server-side session and cookies
    await rf.getRequest('AuthRequest').logout();
  } catch (error) {
    // Even if the API call fails, we should still clear the client-side session
    console.log('Logout API call failed, but continuing with client-side cleanup', error);
  }

  try {
    // Also make a direct call to our local logout API to ensure cookies are cleared
    const { clearCookiesFromClient } = await import('@/utils/cookie');
    await clearCookiesFromClient();
  } catch (error) {
    console.log('Direct logout API call failed, but continuing with cleanup', error);
  }

  // Always clear the user state and authorization header
  dispatch(clearUser());

  // Clear the authorization header from axios
  const { setAuthorizationToRequest } = await import('@/utils/auth');
  setAuthorizationToRequest(null);

  // Clear tokens from localStorage
  const Storage = (await import('@/libs/storage')).default;
  Storage.clearTokens();

  // Redirect to login page
  if (typeof window !== 'undefined') {
    window.location.href = '/signin';
  }
});

export const { setUserInfo, clearUser } = userSlice.actions;

export default userSlice.reducer;
